"""
修复后的数据加载器，解决Windows多进程和内存问题
"""
import torch
import torch.multiprocessing as mp
from torch.utils.data import DataLoader
import os
import platform

def get_optimal_dataloader_settings():
    """
    根据操作系统和硬件配置获取最优的DataLoader设置
    """
    system = platform.system()

    if system == "Windows":
        # Windows系统的设置
        return {
            'num_workers': 0,  # Windows上禁用多进程
            'pin_memory': False,  # 禁用pin_memory
            'persistent_workers': False,
            'prefetch_factor': None,  # 当num_workers=0时必须设为None
        }
    else:
        # Linux/macOS系统的设置
        return {
            'num_workers': min(4, os.cpu_count()),
            'pin_memory': True,
            'persistent_workers': True,
            'prefetch_factor': 2,
        }

def get_safe_batch_size(requested_batch_size, force_original=False):
    """
    获取安全的batch size，考虑系统限制和GPU内存
    """
    if force_original:
        return requested_batch_size

    # 检查可用GPU内存
    if torch.cuda.is_available():
        try:
            # 获取GPU内存信息
            gpu_memory = torch.cuda.get_device_properties(0).total_memory
            gpu_memory_gb = gpu_memory / (1024**3)

            # 根据GPU内存动态调整最大batch size
            if gpu_memory_gb >= 8:
                max_batch_size = 256
            elif gpu_memory_gb >= 4:
                max_batch_size = 128
            else:
                max_batch_size = 64
        except:
            # 如果无法获取GPU信息，使用保守设置
            max_batch_size = 128 if platform.system() == "Windows" else 256
    else:
        # CPU模式下的设置
        max_batch_size = 64 if platform.system() == "Windows" else 128

    return min(requested_batch_size, max_batch_size)

def create_safe_dataloader(dataset, batch_size, shuffle=True, collate_fn=None, force_original_batch_size=False, **kwargs):
    """
    创建一个安全的DataLoader，自动处理多进程和内存问题

    Args:
        dataset: 数据集
        batch_size: 请求的batch size
        shuffle: 是否打乱数据
        collate_fn: 数据整理函数
        force_original_batch_size: 是否强制使用原始batch size（忽略内存限制）
        **kwargs: 其他DataLoader参数
    """
    # 获取最优设置
    optimal_settings = get_optimal_dataloader_settings()

    # 合并用户提供的参数
    final_kwargs = {**optimal_settings, **kwargs}

    # 获取安全的batch size
    actual_batch_size = get_safe_batch_size(batch_size, force_original_batch_size)

    if actual_batch_size != batch_size:
        print(f"Warning: Batch size reduced from {batch_size} to {actual_batch_size} to avoid memory issues")
        print(f"Tip: Use force_original_batch_size=True to override this limitation")

    # 创建DataLoader
    dataloader = DataLoader(
        dataset,
        batch_size=actual_batch_size,
        shuffle=shuffle,
        collate_fn=collate_fn,
        **final_kwargs
    )

    return dataloader

def setup_multiprocessing():
    """
    设置多进程环境
    """
    if platform.system() == "Windows":
        # Windows上设置spawn方法
        try:
            mp.set_start_method('spawn', force=True)
        except RuntimeError:
            pass  # 如果已经设置过，忽略错误
    
    # 设置共享内存策略
    if hasattr(torch, 'multiprocessing'):
        torch.multiprocessing.set_sharing_strategy('file_system')

# 在模块导入时自动设置
setup_multiprocessing()
